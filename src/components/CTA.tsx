
import { But<PERSON> } from "@/components/ui/button";
import { Gift, Sparkles } from "lucide-react";

const CTA = () => {
  return (
    <section className="py-20 bg-gradient-to-br from-emerald-600 via-emerald-700 to-green-800 relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute top-10 right-10 w-32 h-32 bg-white rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-10 left-10 w-40 h-40 bg-amber-300 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 w-24 h-24 bg-rose-300 rounded-full blur-2xl animate-pulse delay-500"></div>
      </div>
      
      <div className="container mx-auto px-6 text-center relative z-10">
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center justify-center gap-3 mb-6">
            <Sparkles className="w-8 h-8 text-amber-300" />
            <Gift className="w-12 h-12 text-white" />
            <Sparkles className="w-8 h-8 text-amber-300" />
          </div>
          
          <h2 className="text-4xl md:text-6xl font-bold text-white mb-6 leading-tight">
            Ready to Make This
            <span className="block text-transparent bg-gradient-to-r from-amber-300 to-orange-300 bg-clip-text">
              Eid Unforgettable?
            </span>
          </h2>
          
          <p className="text-xl md:text-2xl text-emerald-100 mb-10 max-w-3xl mx-auto leading-relaxed">
            Join thousands of families who are already spreading joy with Eideya. 
            Create your first digital Eid gift in minutes and watch faces light up with happiness.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              size="lg" 
              className="bg-white text-emerald-700 hover:bg-emerald-50 px-8 py-6 text-lg rounded-full shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105"
            >
              Start Spreading Joy Now
            </Button>
            <Button 
              variant="outline" 
              size="lg"
              className="border-2 border-white text-white hover:bg-white hover:text-emerald-700 px-8 py-6 text-lg rounded-full shadow-lg hover:shadow-xl transition-all duration-300"
            >
              Learn More
            </Button>
          </div>
          
          <div className="mt-12 text-emerald-100">
            <p className="text-lg">🎁 Special Launch Offer: First 3 Eidiyas FREE for new users!</p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CTA;
