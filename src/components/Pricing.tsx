
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Check, Crown, Gift, Building } from "lucide-react";
import { Link } from "react-router-dom";

const Pricing = () => {
  const plans = [
    {
      name: "Standard Cards",
      price: "20",
      currency: "EGP",
      description: "Perfect for personal Eid greetings",
      icon: <Gift className="w-6 h-6" />,
      features: [
        "Beautiful Eid card templates",
        "Basic personalization",
        "Instant delivery via WhatsApp/SMS",
        "Money transfer (EGP 5 fee)",
        "24/7 support"
      ],
      color: "from-emerald-500 to-green-600",
      popular: false
    },
    {
      name: "Premium Cards",
      price: "50",
      currency: "EGP",
      description: "Custom designs for special moments",
      icon: <Crown className="w-6 h-6" />,
      features: [
        "Everything in Standard",
        "Custom design creation",
        "Voice note recordings",
        "Animated stickers & effects",
        "Scheduled delivery",
        "Priority support"
      ],
      color: "from-amber-500 to-orange-500",
      popular: true
    },
    {
      name: "VIP Subscription",
      price: "99",
      currency: "EGP/year",
      description: "Unlimited access for Eid enthusiasts",
      icon: <Crown className="w-6 h-6" />,
      features: [
        "Unlimited standard & premium cards",
        "Early access to new designs",
        "Bulk sending capabilities",
        "Advanced scheduling options",
        "Priority customer support",
        "Exclusive seasonal templates"
      ],
      color: "from-purple-500 to-pink-500",
      popular: false
    }
  ];

  return (
    <section className="py-20 bg-white/50 backdrop-blur-sm">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Choose Your
            <span className="block bg-gradient-to-r from-emerald-600 to-amber-500 bg-clip-text text-transparent">
              Perfect Plan
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            From simple greetings to premium experiences, we have the right option for every Eid celebration.
          </p>
        </div>
        
        <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {plans.map((plan, index) => (
            <Card 
              key={index} 
              className={`relative p-8 border-0 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 bg-white/80 backdrop-blur-sm ${
                plan.popular ? 'ring-2 ring-amber-400 shadow-amber-200' : ''
              }`}
            >
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <div className="bg-gradient-to-r from-amber-500 to-orange-500 text-white px-4 py-2 rounded-full text-sm font-semibold shadow-lg">
                    Most Popular
                  </div>
                </div>
              )}
              
              <div className={`w-12 h-12 rounded-xl bg-gradient-to-br ${plan.color} flex items-center justify-center text-white mb-6 shadow-lg`}>
                {plan.icon}
              </div>
              
              <h3 className="text-2xl font-bold text-gray-900 mb-2">{plan.name}</h3>
              <p className="text-gray-600 mb-6">{plan.description}</p>
              
              <div className="mb-6">
                <span className="text-4xl font-bold text-gray-900">{plan.price}</span>
                <span className="text-gray-500 ml-2">{plan.currency}</span>
              </div>
              
              <ul className="space-y-3 mb-8">
                {plan.features.map((feature, idx) => (
                  <li key={idx} className="flex items-center gap-3">
                    <div className={`w-5 h-5 rounded-full bg-gradient-to-br ${plan.color} flex items-center justify-center`}>
                      <Check className="w-3 h-3 text-white" />
                    </div>
                    <span className="text-gray-700">{feature}</span>
                  </li>
                ))}
              </ul>
              
              <Link to="/create-card">
                <Button 
                  className={`w-full bg-gradient-to-r ${plan.color} hover:opacity-90 text-white py-6 text-lg rounded-full shadow-lg hover:shadow-xl transition-all duration-300`}
                >
                  Get Started
                </Button>
              </Link>
            </Card>
          ))}
        </div>
        
        {/* Corporate section */}
        <div className="mt-16 text-center">
          <Card className="p-8 bg-gradient-to-r from-indigo-50 to-purple-50 border-0 shadow-lg max-w-2xl mx-auto">
            <div className="w-16 h-16 rounded-2xl bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center text-white mb-6 mx-auto shadow-lg">
              <Building className="w-8 h-8" />
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-4">Corporate Packages</h3>
            <p className="text-gray-600 mb-6">
              Special pricing for companies, schools, and organizations. Bulk sending with custom branding available.
            </p>
            <Button variant="outline" className="border-2 border-indigo-600 text-indigo-700 hover:bg-indigo-50">
              Contact Sales
            </Button>
          </Card>
        </div>
      </div>
    </section>
  );
};

export default Pricing;
