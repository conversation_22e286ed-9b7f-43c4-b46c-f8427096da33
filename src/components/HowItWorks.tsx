
import { Card } from "@/components/ui/card";
import { ArrowRight } from "lucide-react";

const HowItWorks = () => {
  const steps = [
    {
      step: "01",
      title: "Choose Your Style",
      description: "Select from beautiful Eid card templates or create your own custom design with photos and messages.",
      color: "from-emerald-500 to-green-600"
    },
    {
      step: "02", 
      title: "Add Your Touch",
      description: "Personalize with names, voice notes, animated stickers, and decide how much eideya to send.",
      color: "from-amber-500 to-orange-500"
    },
    {
      step: "03",
      title: "Schedule & Send",
      description: "Choose when to deliver (instantly or scheduled) and send via WhatsApp, email, or SMS.",
      color: "from-rose-500 to-pink-500"
    }
  ];

  return (
    <section className="py-20 bg-gradient-to-br from-emerald-50 to-amber-50">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Simple as
            <span className="block bg-gradient-to-r from-emerald-600 to-amber-500 bg-clip-text text-transparent">
              1, 2, 3
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Sending the perfect Eid gift has never been easier. Follow these simple steps and spread joy in minutes.
          </p>
        </div>
        
        <div className="max-w-4xl mx-auto">
          <div className="grid md:grid-cols-3 gap-8">
            {steps.map((step, index) => (
              <div key={index} className="relative">
                <Card className="p-8 border-0 shadow-lg bg-white/80 backdrop-blur-sm h-full">
                  <div className={`w-16 h-16 rounded-full bg-gradient-to-br ${step.color} flex items-center justify-center text-white text-xl font-bold mb-6 shadow-lg`}>
                    {step.step}
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-4">{step.title}</h3>
                  <p className="text-gray-600 leading-relaxed">{step.description}</p>
                </Card>
                
                {/* Arrow connector (hidden on mobile) */}
                {index < steps.length - 1 && (
                  <div className="hidden md:block absolute top-1/2 -right-4 transform -translate-y-1/2 z-10">
                    <div className="w-8 h-8 bg-white rounded-full shadow-lg flex items-center justify-center">
                      <ArrowRight className="w-4 h-4 text-emerald-600" />
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
    );
};

export default HowItWorks;
