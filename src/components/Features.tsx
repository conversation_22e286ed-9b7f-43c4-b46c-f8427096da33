
import { Card } from "@/components/ui/card";
import { 
  Palette, 
  DollarSign, 
  Clock, 
  Shuffle, 
  Building, 
  MessageCircle,
  Mic,
  Calendar
} from "lucide-react";

const Features = () => {
  const features = [
    {
      icon: <Palette className="w-8 h-8" />,
      title: "Custom Digital Cards",
      description: "Create beautiful personalized Eid cards with names, photos, voice notes, and animated stickers.",
      color: "from-purple-500 to-pink-500"
    },
    {
      icon: <DollarSign className="w-8 h-8" />,
      title: "Money Transfers",
      description: "Send eidiyas instantly using Vodafone Cash, Instapay, or direct bank transfers.",
      color: "from-emerald-500 to-green-600"
    },
    {
      icon: <Clock className="w-8 h-8" />,
      title: "Scheduled Delivery",
      description: "Set specific times for your gifts to be delivered - perfect for Eid morning surprises!",
      color: "from-blue-500 to-cyan-500"
    },
    {
      icon: <Shuffle className="w-8 h-8" />,
      title: "Surprise Me Mode",
      description: "Let our platform pick the perfect random Eid gift or card design for your recipient.",
      color: "from-amber-500 to-orange-500"
    },
    {
      icon: <Building className="w-8 h-8" />,
      title: "Business Mode",
      description: "Bulk sending solutions for HR teams, schools, and organizations to reach all employees.",
      color: "from-indigo-500 to-purple-600"
    },
    {
      icon: <MessageCircle className="w-8 h-8" />,
      title: "Multi-Platform Delivery",
      description: "Send via WhatsApp, email, or SMS - reach your loved ones wherever they are.",
      color: "from-rose-500 to-red-500"
    }
  ];

  return (
    <section className="py-20 bg-white/50 backdrop-blur-sm">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Everything You Need to
            <span className="block bg-gradient-to-r from-emerald-600 to-amber-500 bg-clip-text text-transparent">
              Spread Eid Joy
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Our platform combines the warmth of traditional Eid celebrations with modern digital convenience.
          </p>
        </div>
        
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <Card 
              key={index} 
              className="p-8 border-0 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 bg-white/80 backdrop-blur-sm"
            >
              <div className={`w-16 h-16 rounded-2xl bg-gradient-to-br ${feature.color} flex items-center justify-center text-white mb-6 shadow-lg`}>
                {feature.icon}
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">{feature.title}</h3>
              <p className="text-gray-600 leading-relaxed">{feature.description}</p>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Features;
