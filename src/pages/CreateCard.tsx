import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import {
  ArrowLeft,
  Upload,
  Send,
  Calendar,
  Gift,
  Heart,
  Star,
  Building2,
  Moon,
  Sparkles,
} from "lucide-react";
import { Link, useNavigate } from "react-router-dom";

const CreateCard = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    recipientName: "",
    recipientPhone: "",
    senderName: "",
    personalMessage: "",
    eideyaAmount: "",
    deliveryMethod: "whatsapp",
    scheduledDate: "",
    selectedTemplate: "template1",
  });

  const templates = [
    {
      id: "template1",
      name: "Golden Mosque",
      nameAr: "المسجد الذهبي",
      color: "from-amber-500 via-yellow-500 to-orange-600",
      icon: <Building2 className="w-6 h-6" />,
    },
    {
      id: "template2",
      name: "Emerald Crescent",
      nameAr: "الهلال الزمردي",
      color: "from-emerald-600 via-green-500 to-teal-600",
      icon: <Moon className="w-6 h-6" />,
    },
    {
      id: "template3",
      name: "Royal Purple",
      nameAr: "البنفسجي الملكي",
      color: "from-purple-600 via-violet-500 to-indigo-600",
      icon: <Sparkles className="w-6 h-6" />,
    },
    {
      id: "template4",
      name: "Burgundy Elegance",
      nameAr: "الأناقة البرغندية",
      color: "from-red-800 via-rose-600 to-pink-600",
      icon: <Gift className="w-6 h-6" />,
    },
  ];

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const handleSend = () => {
    // Navigate to send page with form data
    navigate("/send-card", { state: { cardData: formData } });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-amber-50 to-rose-50 relative overflow-hidden">
      {/* Islamic Geometric Pattern Background */}
      <div className="absolute inset-0 opacity-5">
        <div
          className="absolute top-0 left-0 w-full h-full"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23059669' fill-opacity='0.4'%3E%3Cpath d='M30 30l15-15v30l-15-15zm0 0l-15 15h30l-15-15z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            backgroundSize: "60px 60px",
          }}
        ></div>
      </div>

      {/* Floating decorative elements */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-br from-emerald-400/20 to-green-600/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-gradient-to-br from-amber-400/20 to-orange-600/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 w-24 h-24 bg-gradient-to-br from-rose-400/20 to-pink-600/20 rounded-full blur-2xl animate-pulse delay-500"></div>
      </div>
      {/* Header */}
      <div className="bg-white/90 backdrop-blur-md border-b border-emerald-100 shadow-lg relative z-10">
        <div className="container mx-auto px-6 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Link
                to="/"
                className="flex items-center gap-2 text-emerald-600 hover:text-emerald-700 transition-colors"
              >
                <ArrowLeft className="w-5 h-5" />
                <span className="font-medium">Back to Home</span>
              </Link>
              <div className="h-6 w-px bg-emerald-200"></div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-emerald-700 to-amber-600 bg-clip-text text-transparent">
                  Create Your Eid Card
                </h1>
                <p className="text-sm text-emerald-600">
                  أنشئ بطاقة العيد الخاصة بك
                </p>
              </div>
            </div>
            <div className="hidden md:flex items-center gap-2 text-emerald-700">
              <Gift className="w-5 h-5" />
              <span className="text-sm font-medium">Eid Al-Adha Mubarak</span>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-6 py-8">
        <div className="grid lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
          {/* Form Section */}
          <div className="space-y-6">
            {/* Template Selection */}
            <Card className="border-emerald-100 shadow-lg">
              <CardHeader className="bg-gradient-to-r from-emerald-50 to-amber-50">
                <CardTitle className="flex items-center gap-2">
                  <Star className="w-5 h-5 text-amber-500" />
                  <div>
                    <span className="text-emerald-700">Choose Template</span>
                    <p className="text-sm font-normal text-emerald-600">
                      اختر القالب
                    </p>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="grid grid-cols-2 gap-4">
                  {templates.map((template) => (
                    <button
                      key={template.id}
                      onClick={() =>
                        setFormData({
                          ...formData,
                          selectedTemplate: template.id,
                        })
                      }
                      className={`group p-4 rounded-xl border-2 transition-all duration-300 hover:scale-105 ${
                        formData.selectedTemplate === template.id
                          ? "border-emerald-500 bg-emerald-50 shadow-lg"
                          : "border-gray-200 hover:border-emerald-300 hover:shadow-md"
                      }`}
                    >
                      <div className="relative">
                        <div
                          className={`w-full h-20 rounded-lg bg-gradient-to-br ${template.color} mb-3 shadow-md flex items-center justify-center`}
                        >
                          <div className="text-white opacity-80">
                            {template.icon}
                          </div>
                        </div>
                        {formData.selectedTemplate === template.id && (
                          <div className="absolute -top-2 -right-2 w-6 h-6 bg-emerald-500 rounded-full flex items-center justify-center">
                            <Star className="w-3 h-3 text-white fill-current" />
                          </div>
                        )}
                      </div>
                      <div className="text-center">
                        <p className="text-sm font-semibold text-gray-800">
                          {template.name}
                        </p>
                        <p className="text-xs text-emerald-600 mt-1">
                          {template.nameAr}
                        </p>
                      </div>
                    </button>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Recipient Details */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Heart className="w-5 h-5 text-rose-500" />
                  Recipient Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="recipientName">Recipient Name</Label>
                  <Input
                    id="recipientName"
                    name="recipientName"
                    value={formData.recipientName}
                    onChange={handleInputChange}
                    placeholder="Who are you sending this to?"
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label htmlFor="recipientPhone">Phone Number</Label>
                  <Input
                    id="recipientPhone"
                    name="recipientPhone"
                    value={formData.recipientPhone}
                    onChange={handleInputChange}
                    placeholder="+20 ************"
                    className="mt-1"
                  />
                </div>
              </CardContent>
            </Card>

            {/* Personalization */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Gift className="w-5 h-5 text-emerald-500" />
                  Personalization
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="senderName">Your Name</Label>
                  <Input
                    id="senderName"
                    name="senderName"
                    value={formData.senderName}
                    onChange={handleInputChange}
                    placeholder="From..."
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label htmlFor="personalMessage">Personal Message</Label>
                  <Textarea
                    id="personalMessage"
                    name="personalMessage"
                    value={formData.personalMessage}
                    onChange={handleInputChange}
                    placeholder="Write your Eid wishes here..."
                    className="mt-1 min-h-[100px]"
                  />
                </div>
                <div>
                  <Label htmlFor="eideyaAmount">Eideya Amount (EGP)</Label>
                  <Input
                    id="eideyaAmount"
                    name="eideyaAmount"
                    type="number"
                    value={formData.eideyaAmount}
                    onChange={handleInputChange}
                    placeholder="50"
                    className="mt-1"
                  />
                </div>
              </CardContent>
            </Card>

            {/* Delivery Options */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Send className="w-5 h-5 text-blue-500" />
                  Delivery Options
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="deliveryMethod">Delivery Method</Label>
                  <select
                    id="deliveryMethod"
                    name="deliveryMethod"
                    value={formData.deliveryMethod}
                    onChange={handleInputChange}
                    className="mt-1 w-full h-10 px-3 py-2 border border-input bg-background rounded-md text-sm"
                  >
                    <option value="whatsapp">WhatsApp</option>
                    <option value="sms">SMS</option>
                    <option value="email">Email</option>
                  </select>
                </div>
                <div>
                  <Label htmlFor="scheduledDate">
                    Schedule Delivery (Optional)
                  </Label>
                  <Input
                    id="scheduledDate"
                    name="scheduledDate"
                    type="datetime-local"
                    value={formData.scheduledDate}
                    onChange={handleInputChange}
                    className="mt-1"
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Preview Section */}
          <div className="space-y-6">
            {/* Card Preview */}
            <Card className="border-emerald-100 shadow-xl">
              <CardHeader className="bg-gradient-to-r from-emerald-50 to-amber-50">
                <CardTitle className="flex items-center gap-2">
                  <Gift className="w-5 h-5 text-emerald-600" />
                  <div>
                    <span className="text-emerald-700">Preview Your Card</span>
                    <p className="text-sm font-normal text-emerald-600">
                      معاينة البطاقة
                    </p>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="relative">
                  <div
                    className={`w-full h-96 rounded-xl bg-gradient-to-br ${
                      templates.find((t) => t.id === formData.selectedTemplate)
                        ?.color || "from-amber-500 via-yellow-500 to-orange-600"
                    } p-8 text-white shadow-2xl relative overflow-hidden`}
                  >
                    {/* Decorative Islamic pattern overlay */}
                    <div className="absolute inset-0 opacity-10">
                      <div
                        className="w-full h-full"
                        style={{
                          backgroundImage: `url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='white' fill-opacity='0.3'%3E%3Cpath d='M20 20l10-10v20l-10-10zm0 0l-10 10h20l-10-10z'/%3E%3C/g%3E%3C/svg%3E")`,
                          backgroundSize: "40px 40px",
                        }}
                      ></div>
                    </div>

                    <div className="relative z-10 flex flex-col h-full justify-between">
                      <div className="text-center">
                        <div className="mb-4">
                          {templates.find(
                            (t) => t.id === formData.selectedTemplate
                          )?.icon && (
                            <div className="inline-flex items-center justify-center w-12 h-12 bg-white/20 rounded-full mb-3">
                              {
                                templates.find(
                                  (t) => t.id === formData.selectedTemplate
                                )?.icon
                              }
                            </div>
                          )}
                        </div>
                        <h3 className="text-2xl font-bold mb-2">
                          🕌 Eid Al-Adha Mubarak! 🌙
                        </h3>
                        <p className="text-lg opacity-90 mb-1">
                          عيد أضحى مبارك
                        </p>
                        <p className="text-lg font-semibold">
                          To: {formData.recipientName || "Recipient Name"}
                        </p>
                      </div>

                      <div className="space-y-4 text-center">
                        <div className="bg-white/15 backdrop-blur-sm rounded-lg p-4">
                          <p className="text-sm leading-relaxed">
                            {formData.personalMessage ||
                              "May this blessed Eid bring you joy, peace, and prosperity. Your personal message will appear here..."}
                          </p>
                        </div>

                        {formData.eideyaAmount && (
                          <div className="bg-white/25 backdrop-blur-sm rounded-lg p-4 border border-white/30">
                            <p className="text-xl font-bold">
                              🎁 Eideya: {formData.eideyaAmount} EGP
                            </p>
                          </div>
                        )}
                      </div>

                      <div className="text-center">
                        <p className="text-sm opacity-90">
                          With love from: {formData.senderName || "Your Name"}
                        </p>
                        <p className="text-xs opacity-75 mt-1">
                          Sent with ❤️ via Eideya
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Additional Features */}
            <Card>
              <CardHeader>
                <CardTitle>Add More</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button variant="outline" className="w-full justify-start">
                  <Upload className="w-4 h-4 mr-2" />
                  Upload Photo
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <Calendar className="w-4 h-4 mr-2" />
                  Record Voice Note
                </Button>
              </CardContent>
            </Card>

            {/* Send Button */}
            <Button
              onClick={handleSend}
              size="lg"
              className="w-full bg-gradient-to-r from-emerald-600 to-green-600 hover:from-emerald-700 hover:to-green-700 py-6 text-lg"
            >
              <Send className="w-5 h-5 mr-2" />
              Send Eid Card & Payment
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreateCard;
