
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { ArrowLeft, Upload, Send, Calendar, Gift, Heart, Star } from "lucide-react";
import { Link } from "react-router-dom";

const CreateCard = () => {
  const [formData, setFormData] = useState({
    recipientName: "",
    recipientPhone: "",
    senderName: "",
    personalMessage: "",
    eideyaAmount: "",
    deliveryMethod: "whatsapp",
    scheduledDate: "",
    selectedTemplate: "template1"
  });

  const templates = [
    { id: "template1", name: "Golden Mosque", color: "from-amber-400 to-orange-500" },
    { id: "template2", name: "Green Crescent", color: "from-emerald-400 to-green-500" },
    { id: "template3", name: "Purple Stars", color: "from-purple-400 to-pink-500" },
    { id: "template4", name: "Blue Lantern", color: "from-blue-400 to-indigo-500" }
  ];

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSend = () => {
    console.log("Sending card with data:", formData);
    // Here you would integrate with your backend/payment system
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-white to-amber-50">
      {/* Header */}
      <div className="bg-white/80 backdrop-blur-sm border-b shadow-sm">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center gap-4">
            <Link to="/" className="flex items-center gap-2 text-emerald-600 hover:text-emerald-700">
              <ArrowLeft className="w-5 h-5" />
              <span>Back to Home</span>
            </Link>
            <div className="h-6 w-px bg-gray-300"></div>
            <h1 className="text-xl font-bold text-gray-900">Create Your Eid Card</h1>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-6 py-8">
        <div className="grid lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
          
          {/* Form Section */}
          <div className="space-y-6">
            
            {/* Template Selection */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Star className="w-5 h-5 text-amber-500" />
                  Choose Template
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-3">
                  {templates.map((template) => (
                    <button
                      key={template.id}
                      onClick={() => setFormData({...formData, selectedTemplate: template.id})}
                      className={`p-4 rounded-lg border-2 transition-all ${
                        formData.selectedTemplate === template.id 
                          ? 'border-emerald-500 bg-emerald-50' 
                          : 'border-gray-200 hover:border-emerald-300'
                      }`}
                    >
                      <div className={`w-full h-16 rounded bg-gradient-to-r ${template.color} mb-2`}></div>
                      <p className="text-sm font-medium">{template.name}</p>
                    </button>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Recipient Details */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Heart className="w-5 h-5 text-rose-500" />
                  Recipient Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="recipientName">Recipient Name</Label>
                  <Input
                    id="recipientName"
                    name="recipientName"
                    value={formData.recipientName}
                    onChange={handleInputChange}
                    placeholder="Who are you sending this to?"
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label htmlFor="recipientPhone">Phone Number</Label>
                  <Input
                    id="recipientPhone"
                    name="recipientPhone"
                    value={formData.recipientPhone}
                    onChange={handleInputChange}
                    placeholder="+20 ************"
                    className="mt-1"
                  />
                </div>
              </CardContent>
            </Card>

            {/* Personalization */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Gift className="w-5 h-5 text-emerald-500" />
                  Personalization
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="senderName">Your Name</Label>
                  <Input
                    id="senderName"
                    name="senderName"
                    value={formData.senderName}
                    onChange={handleInputChange}
                    placeholder="From..."
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label htmlFor="personalMessage">Personal Message</Label>
                  <Textarea
                    id="personalMessage"
                    name="personalMessage"
                    value={formData.personalMessage}
                    onChange={handleInputChange}
                    placeholder="Write your Eid wishes here..."
                    className="mt-1 min-h-[100px]"
                  />
                </div>
                <div>
                  <Label htmlFor="eideyaAmount">Eideya Amount (EGP)</Label>
                  <Input
                    id="eideyaAmount"
                    name="eideyaAmount"
                    type="number"
                    value={formData.eideyaAmount}
                    onChange={handleInputChange}
                    placeholder="50"
                    className="mt-1"
                  />
                </div>
              </CardContent>
            </Card>

            {/* Delivery Options */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Send className="w-5 h-5 text-blue-500" />
                  Delivery Options
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="deliveryMethod">Delivery Method</Label>
                  <select
                    id="deliveryMethod"
                    name="deliveryMethod"
                    value={formData.deliveryMethod}
                    onChange={handleInputChange}
                    className="mt-1 w-full h-10 px-3 py-2 border border-input bg-background rounded-md text-sm"
                  >
                    <option value="whatsapp">WhatsApp</option>
                    <option value="sms">SMS</option>
                    <option value="email">Email</option>
                  </select>
                </div>
                <div>
                  <Label htmlFor="scheduledDate">Schedule Delivery (Optional)</Label>
                  <Input
                    id="scheduledDate"
                    name="scheduledDate"
                    type="datetime-local"
                    value={formData.scheduledDate}
                    onChange={handleInputChange}
                    className="mt-1"
                  />
                </div>
              </CardContent>
            </Card>

          </div>

          {/* Preview Section */}
          <div className="space-y-6">
            
            {/* Card Preview */}
            <Card>
              <CardHeader>
                <CardTitle>Preview Your Card</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="relative">
                  <div className={`w-full h-80 rounded-lg bg-gradient-to-br ${
                    templates.find(t => t.id === formData.selectedTemplate)?.color || "from-amber-400 to-orange-500"
                  } p-6 text-white shadow-xl`}>
                    <div className="flex flex-col h-full justify-between">
                      <div>
                        <h3 className="text-xl font-bold mb-2">Eid Mubarak!</h3>
                        <p className="text-lg">To: {formData.recipientName || "Recipient Name"}</p>
                      </div>
                      <div className="space-y-2">
                        <p className="text-sm opacity-90">
                          {formData.personalMessage || "Your personal message will appear here..."}
                        </p>
                        {formData.eideyaAmount && (
                          <div className="bg-white/20 rounded-lg p-3 text-center">
                            <p className="text-lg font-bold">Eideya: {formData.eideyaAmount} EGP</p>
                          </div>
                        )}
                      </div>
                      <div className="text-right">
                        <p className="text-sm">From: {formData.senderName || "Your Name"}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Additional Features */}
            <Card>
              <CardHeader>
                <CardTitle>Add More</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button variant="outline" className="w-full justify-start">
                  <Upload className="w-4 h-4 mr-2" />
                  Upload Photo
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <Calendar className="w-4 h-4 mr-2" />
                  Record Voice Note
                </Button>
              </CardContent>
            </Card>

            {/* Send Button */}
            <Button 
              onClick={handleSend}
              size="lg" 
              className="w-full bg-gradient-to-r from-emerald-600 to-green-600 hover:from-emerald-700 hover:to-green-700 py-6 text-lg"
            >
              <Send className="w-5 h-5 mr-2" />
              Send Eid Card & Payment
            </Button>

          </div>
        </div>
      </div>
    </div>
  );
};

export default CreateCard;
