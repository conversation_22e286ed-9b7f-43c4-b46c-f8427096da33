import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { <PERSON>, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { useToast } from "@/hooks/use-toast";
import {
  ArrowLeft,
  Upload,
  Send,
  Calendar,
  Gift,
  Heart,
  Star,
  Building2,
  Moon,
  Sparkles,
  AlertCircle,
  CheckCircle,
  Loader2,
  Phone,
  User,
  MessageSquare,
  DollarSign,
} from "lucide-react";
import { Link, useNavigate } from "react-router-dom";

interface FormData {
  recipientName: string;
  recipientPhone: string;
  senderName: string;
  personalMessage: string;
  eideyaAmount: string;
  deliveryMethod: string;
  scheduledDate: string;
  selectedTemplate: string;
}

interface FormErrors {
  recipientName?: string;
  recipientPhone?: string;
  senderName?: string;
  personalMessage?: string;
  eideyaAmount?: string;
  general?: string;
}

const CreateCard = () => {
  const navigate = useNavigate();
  const { toast } = useToast();

  const [formData, setFormData] = useState<FormData>({
    recipientName: "",
    recipientPhone: "",
    senderName: "",
    personalMessage: "",
    eideyaAmount: "",
    deliveryMethod: "whatsapp",
    scheduledDate: "",
    selectedTemplate: "template1",
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [isValidating, setIsValidating] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [completionProgress, setCompletionProgress] = useState(0);

  // Calculate form completion progress
  useEffect(() => {
    const requiredFields = [
      "recipientName",
      "recipientPhone",
      "senderName",
      "eideyaAmount",
    ];
    const completedFields = requiredFields.filter(
      (field) => formData[field as keyof FormData].trim() !== ""
    );
    const progress = (completedFields.length / requiredFields.length) * 100;
    setCompletionProgress(progress);
  }, [formData]);

  const templates = [
    {
      id: "template1",
      name: "Golden Mosque",
      nameAr: "المسجد الذهبي",
      color: "from-amber-500 via-yellow-500 to-orange-600",
      icon: <Building2 className="w-6 h-6" />,
    },
    {
      id: "template2",
      name: "Emerald Crescent",
      nameAr: "الهلال الزمردي",
      color: "from-emerald-600 via-green-500 to-teal-600",
      icon: <Moon className="w-6 h-6" />,
    },
    {
      id: "template3",
      name: "Royal Purple",
      nameAr: "البنفسجي الملكي",
      color: "from-purple-600 via-violet-500 to-indigo-600",
      icon: <Sparkles className="w-6 h-6" />,
    },
    {
      id: "template4",
      name: "Burgundy Elegance",
      nameAr: "الأناقة البرغندية",
      color: "from-red-800 via-rose-600 to-pink-600",
      icon: <Gift className="w-6 h-6" />,
    },
  ];

  // Validation functions
  const validateField = (name: string, value: string): string | undefined => {
    switch (name) {
      case "recipientName":
        if (!value.trim()) return "Recipient name is required";
        if (value.trim().length < 2)
          return "Name must be at least 2 characters";
        if (!/^[a-zA-Z\u0600-\u06FF\s]+$/.test(value))
          return "Name can only contain letters and spaces";
        return undefined;

      case "recipientPhone":
        if (!value.trim()) return "Phone number is required";
        if (!/^(\+20|0)?1[0-9]{9}$/.test(value.replace(/\s/g, ""))) {
          return "Please enter a valid Egyptian phone number";
        }
        return undefined;

      case "senderName":
        if (!value.trim()) return "Your name is required";
        if (value.trim().length < 2)
          return "Name must be at least 2 characters";
        if (!/^[a-zA-Z\u0600-\u06FF\s]+$/.test(value))
          return "Name can only contain letters and spaces";
        return undefined;

      case "eideyaAmount": {
        if (!value.trim()) return "Eideya amount is required";
        const amount = parseFloat(value);
        if (isNaN(amount)) return "Please enter a valid amount";
        if (amount < 10) return "Minimum amount is 10 EGP";
        if (amount > 10000) return "Maximum amount is 10,000 EGP";
        return undefined;
      }

      case "personalMessage":
        if (value.length > 500)
          return "Message must be less than 500 characters";
        return undefined;

      default:
        return undefined;
    }
  };

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // Validate all fields
    Object.keys(formData).forEach((key) => {
      const error = validateField(key, formData[key as keyof FormData]);
      if (error) {
        newErrors[key as keyof FormErrors] = error;
      }
    });

    // Check scheduled date if provided
    if (formData.scheduledDate) {
      const scheduledTime = new Date(formData.scheduledDate);
      const now = new Date();
      if (scheduledTime <= now) {
        newErrors.general = "Scheduled delivery must be in the future";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;

    setFormData({
      ...formData,
      [name]: value,
    });

    // Real-time validation
    if (errors[name as keyof FormErrors]) {
      const error = validateField(name, value);
      setErrors({
        ...errors,
        [name]: error,
        general: undefined, // Clear general errors when user makes changes
      });
    }
  };

  const handleBlur = (
    e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    const error = validateField(name, value);

    setErrors({
      ...errors,
      [name]: error,
    });
  };

  const formatPhoneNumber = (value: string): string => {
    // Remove all non-digits
    const digits = value.replace(/\D/g, "");

    // Format as Egyptian phone number
    if (digits.startsWith("20")) {
      return `+${digits.slice(0, 2)} ${digits.slice(2, 4)} ${digits.slice(
        4,
        8
      )} ${digits.slice(8, 12)}`;
    } else if (digits.startsWith("01")) {
      return `+20 ${digits.slice(1, 3)} ${digits.slice(3, 7)} ${digits.slice(
        7,
        11
      )}`;
    } else if (digits.startsWith("1")) {
      return `+20 ${digits.slice(0, 2)} ${digits.slice(2, 6)} ${digits.slice(
        6,
        10
      )}`;
    }

    return value;
  };

  const handleSend = async () => {
    setIsValidating(true);
    setIsSubmitting(true);

    try {
      // Validate form
      if (!validateForm()) {
        toast({
          title: "Validation Error",
          description: "Please fix the errors in the form before proceeding.",
          variant: "destructive",
        });
        return;
      }

      // Show success message
      toast({
        title: "Form Validated Successfully! ✅",
        description: "Redirecting to payment page...",
      });

      // Simulate brief loading
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Navigate to send page with form data
      navigate("/send-card", { state: { cardData: formData } });
    } catch (error) {
      toast({
        title: "Error",
        description: "Something went wrong. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsValidating(false);
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-amber-50 to-rose-50 relative overflow-hidden">
      {/* Islamic Geometric Pattern Background */}
      <div className="absolute inset-0 opacity-5">
        <div
          className="absolute top-0 left-0 w-full h-full"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23059669' fill-opacity='0.4'%3E%3Cpath d='M30 30l15-15v30l-15-15zm0 0l-15 15h30l-15-15z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            backgroundSize: "60px 60px",
          }}
        ></div>
      </div>

      {/* Floating decorative elements */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-br from-emerald-400/20 to-green-600/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-gradient-to-br from-amber-400/20 to-orange-600/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 w-24 h-24 bg-gradient-to-br from-rose-400/20 to-pink-600/20 rounded-full blur-2xl animate-pulse delay-500"></div>
      </div>
      {/* Header */}
      <div className="bg-white/90 backdrop-blur-md border-b border-emerald-100 shadow-lg relative z-10">
        <div className="container mx-auto px-6 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Link
                to="/"
                className="flex items-center gap-2 text-emerald-600 hover:text-emerald-700 transition-colors"
              >
                <ArrowLeft className="w-5 h-5" />
                <span className="font-medium">Back to Home</span>
              </Link>
              <div className="h-6 w-px bg-emerald-200"></div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-emerald-700 to-amber-600 bg-clip-text text-transparent">
                  Create Your Eid Card
                </h1>
                <p className="text-sm text-emerald-600">
                  أنشئ بطاقة العيد الخاصة بك
                </p>
              </div>
            </div>
            <div className="hidden md:flex items-center gap-2 text-emerald-700">
              <Gift className="w-5 h-5" />
              <span className="text-sm font-medium">Eid Al-Adha Mubarak</span>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-6 py-8">
        {/* Progress Indicator */}
        <div className="max-w-6xl mx-auto mb-8">
          <div className="bg-white/90 backdrop-blur-md rounded-xl p-6 shadow-lg border border-emerald-100">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h3 className="text-lg font-semibold text-emerald-700">
                  Form Completion
                </h3>
                <p className="text-sm text-emerald-600">
                  Fill all required fields to proceed
                </p>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-emerald-700">
                  {Math.round(completionProgress)}%
                </div>
                <div className="text-xs text-emerald-600">Complete</div>
              </div>
            </div>
            <Progress value={completionProgress} className="h-2" />

            {/* Required Fields Status */}
            <div className="flex flex-wrap gap-2 mt-4">
              <Badge
                variant={formData.recipientName ? "default" : "secondary"}
                className="flex items-center gap-1"
              >
                <User className="w-3 h-3" />
                Recipient Name
                {formData.recipientName && <CheckCircle className="w-3 h-3" />}
              </Badge>
              <Badge
                variant={formData.recipientPhone ? "default" : "secondary"}
                className="flex items-center gap-1"
              >
                <Phone className="w-3 h-3" />
                Phone Number
                {formData.recipientPhone && <CheckCircle className="w-3 h-3" />}
              </Badge>
              <Badge
                variant={formData.senderName ? "default" : "secondary"}
                className="flex items-center gap-1"
              >
                <User className="w-3 h-3" />
                Your Name
                {formData.senderName && <CheckCircle className="w-3 h-3" />}
              </Badge>
              <Badge
                variant={formData.eideyaAmount ? "default" : "secondary"}
                className="flex items-center gap-1"
              >
                <DollarSign className="w-3 h-3" />
                Eideya Amount
                {formData.eideyaAmount && <CheckCircle className="w-3 h-3" />}
              </Badge>
            </div>
          </div>
        </div>

        {/* General Error Alert */}
        {errors.general && (
          <div className="max-w-6xl mx-auto mb-6">
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{errors.general}</AlertDescription>
            </Alert>
          </div>
        )}

        <div className="grid lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
          {/* Form Section */}
          <div className="space-y-6">
            {/* Template Selection */}
            <Card className="border-emerald-100 shadow-lg">
              <CardHeader className="bg-gradient-to-r from-emerald-50 to-amber-50">
                <CardTitle className="flex items-center gap-2">
                  <Star className="w-5 h-5 text-amber-500" />
                  <div>
                    <span className="text-emerald-700">Choose Template</span>
                    <p className="text-sm font-normal text-emerald-600">
                      اختر القالب
                    </p>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="grid grid-cols-2 gap-4">
                  {templates.map((template) => (
                    <button
                      key={template.id}
                      onClick={() =>
                        setFormData({
                          ...formData,
                          selectedTemplate: template.id,
                        })
                      }
                      className={`group p-4 rounded-xl border-2 transition-all duration-300 hover:scale-105 ${
                        formData.selectedTemplate === template.id
                          ? "border-emerald-500 bg-emerald-50 shadow-lg"
                          : "border-gray-200 hover:border-emerald-300 hover:shadow-md"
                      }`}
                    >
                      <div className="relative">
                        <div
                          className={`w-full h-24 rounded-xl bg-gradient-to-br ${template.color} mb-3 shadow-lg relative overflow-hidden`}
                        >
                          {/* Mini Islamic pattern */}
                          <div className="absolute inset-0 opacity-20">
                            <div
                              className="w-full h-full"
                              style={{
                                backgroundImage: `url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='white' fill-opacity='0.3'%3E%3Cpath d='M10 10l5-5v10l-5-5zm0 0l-5 5h10l-5-5z'/%3E%3C/g%3E%3C/svg%3E")`,
                                backgroundSize: "20px 20px",
                              }}
                            ></div>
                          </div>

                          {/* Mini border */}
                          <div className="absolute inset-1 border border-white/30 rounded-lg"></div>

                          {/* Icon */}
                          <div className="relative z-10 h-full flex items-center justify-center">
                            <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                              <div className="text-white">{template.icon}</div>
                            </div>
                          </div>
                        </div>
                        {formData.selectedTemplate === template.id && (
                          <div className="absolute -top-2 -right-2 w-6 h-6 bg-emerald-500 rounded-full flex items-center justify-center shadow-lg animate-pulse">
                            <Star className="w-3 h-3 text-white fill-current" />
                          </div>
                        )}
                      </div>
                      <div className="text-center">
                        <p className="text-sm font-semibold text-gray-800">
                          {template.name}
                        </p>
                        <p className="text-xs text-emerald-600 mt-1">
                          {template.nameAr}
                        </p>
                      </div>
                    </button>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Recipient Details */}
            <Card className="border-emerald-100 shadow-lg">
              <CardHeader className="bg-gradient-to-r from-emerald-50 to-amber-50">
                <CardTitle className="flex items-center gap-2">
                  <Heart className="w-5 h-5 text-rose-500" />
                  <div>
                    <span className="text-emerald-700">Recipient Details</span>
                    <p className="text-sm font-normal text-emerald-600">
                      تفاصيل المستلم
                    </p>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6 space-y-4">
                <div>
                  <Label
                    htmlFor="recipientName"
                    className="text-emerald-700 font-medium flex items-center gap-1"
                  >
                    <User className="w-4 h-4" />
                    Recipient Name *
                  </Label>
                  <Input
                    id="recipientName"
                    name="recipientName"
                    value={formData.recipientName}
                    onChange={handleInputChange}
                    onBlur={handleBlur}
                    placeholder="Who are you sending this to?"
                    className={`mt-2 ${
                      errors.recipientName
                        ? "border-red-300 focus:border-red-500 focus:ring-red-500"
                        : "border-emerald-200 focus:border-emerald-500 focus:ring-emerald-500"
                    }`}
                  />
                  {errors.recipientName && (
                    <p className="text-red-500 text-sm mt-1 flex items-center gap-1">
                      <AlertCircle className="w-3 h-3" />
                      {errors.recipientName}
                    </p>
                  )}
                </div>
                <div>
                  <Label
                    htmlFor="recipientPhone"
                    className="text-emerald-700 font-medium flex items-center gap-1"
                  >
                    <Phone className="w-4 h-4" />
                    Phone Number *
                  </Label>
                  <Input
                    id="recipientPhone"
                    name="recipientPhone"
                    value={formData.recipientPhone}
                    onChange={handleInputChange}
                    onBlur={handleBlur}
                    placeholder="+20 ************"
                    className={`mt-2 ${
                      errors.recipientPhone
                        ? "border-red-300 focus:border-red-500 focus:ring-red-500"
                        : "border-emerald-200 focus:border-emerald-500 focus:ring-emerald-500"
                    }`}
                  />
                  {errors.recipientPhone && (
                    <p className="text-red-500 text-sm mt-1 flex items-center gap-1">
                      <AlertCircle className="w-3 h-3" />
                      {errors.recipientPhone}
                    </p>
                  )}
                  <p className="text-xs text-emerald-600 mt-1">
                    Egyptian phone numbers only (e.g., 01234567890)
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Personalization */}
            <Card className="border-emerald-100 shadow-lg">
              <CardHeader className="bg-gradient-to-r from-emerald-50 to-amber-50">
                <CardTitle className="flex items-center gap-2">
                  <Gift className="w-5 h-5 text-emerald-500" />
                  <div>
                    <span className="text-emerald-700">Personalization</span>
                    <p className="text-sm font-normal text-emerald-600">
                      التخصيص الشخصي
                    </p>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6 space-y-4">
                <div>
                  <Label
                    htmlFor="senderName"
                    className="text-emerald-700 font-medium flex items-center gap-1"
                  >
                    <User className="w-4 h-4" />
                    Your Name *
                  </Label>
                  <Input
                    id="senderName"
                    name="senderName"
                    value={formData.senderName}
                    onChange={handleInputChange}
                    onBlur={handleBlur}
                    placeholder="From..."
                    className={`mt-2 ${
                      errors.senderName
                        ? "border-red-300 focus:border-red-500 focus:ring-red-500"
                        : "border-emerald-200 focus:border-emerald-500 focus:ring-emerald-500"
                    }`}
                  />
                  {errors.senderName && (
                    <p className="text-red-500 text-sm mt-1 flex items-center gap-1">
                      <AlertCircle className="w-3 h-3" />
                      {errors.senderName}
                    </p>
                  )}
                </div>
                <div>
                  <Label
                    htmlFor="personalMessage"
                    className="text-emerald-700 font-medium flex items-center gap-1"
                  >
                    <MessageSquare className="w-4 h-4" />
                    Personal Message
                    <span className="text-xs text-gray-500">
                      ({formData.personalMessage.length}/500)
                    </span>
                  </Label>
                  <Textarea
                    id="personalMessage"
                    name="personalMessage"
                    value={formData.personalMessage}
                    onChange={handleInputChange}
                    onBlur={handleBlur}
                    placeholder="Write your heartfelt Eid Al-Adha wishes here... May Allah accept your sacrifices and bless you with happiness."
                    className={`mt-2 min-h-[120px] ${
                      errors.personalMessage
                        ? "border-red-300 focus:border-red-500 focus:ring-red-500"
                        : "border-emerald-200 focus:border-emerald-500 focus:ring-emerald-500"
                    }`}
                  />
                  {errors.personalMessage && (
                    <p className="text-red-500 text-sm mt-1 flex items-center gap-1">
                      <AlertCircle className="w-3 h-3" />
                      {errors.personalMessage}
                    </p>
                  )}
                  <p className="text-xs text-emerald-600 mt-1">
                    Share your heartfelt Eid wishes and blessings
                  </p>
                </div>
                <div>
                  <Label
                    htmlFor="eideyaAmount"
                    className="text-emerald-700 font-medium flex items-center gap-1"
                  >
                    <DollarSign className="w-4 h-4" />
                    Eideya Amount (EGP) *
                  </Label>
                  <div className="relative mt-2">
                    <Input
                      id="eideyaAmount"
                      name="eideyaAmount"
                      type="number"
                      min="10"
                      max="10000"
                      value={formData.eideyaAmount}
                      onChange={handleInputChange}
                      onBlur={handleBlur}
                      placeholder="50"
                      className={`pl-8 ${
                        errors.eideyaAmount
                          ? "border-red-300 focus:border-red-500 focus:ring-red-500"
                          : "border-emerald-200 focus:border-emerald-500 focus:ring-emerald-500"
                      }`}
                    />
                    <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-emerald-600 font-medium">
                      💰
                    </span>
                  </div>
                  {errors.eideyaAmount && (
                    <p className="text-red-500 text-sm mt-1 flex items-center gap-1">
                      <AlertCircle className="w-3 h-3" />
                      {errors.eideyaAmount}
                    </p>
                  )}
                  <p className="text-xs text-emerald-600 mt-1">
                    Recommended: 50-200 EGP for family, 20-100 EGP for friends
                    (Min: 10 EGP, Max: 10,000 EGP)
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Delivery Options */}
            <Card className="border-emerald-100 shadow-lg">
              <CardHeader className="bg-gradient-to-r from-emerald-50 to-amber-50">
                <CardTitle className="flex items-center gap-2">
                  <Send className="w-5 h-5 text-blue-500" />
                  <div>
                    <span className="text-emerald-700">Delivery Options</span>
                    <p className="text-sm font-normal text-emerald-600">
                      خيارات التوصيل
                    </p>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6 space-y-4">
                <div>
                  <Label
                    htmlFor="deliveryMethod"
                    className="text-emerald-700 font-medium"
                  >
                    Delivery Method
                  </Label>
                  <select
                    id="deliveryMethod"
                    name="deliveryMethod"
                    value={formData.deliveryMethod}
                    onChange={handleInputChange}
                    className="mt-2 w-full h-12 px-4 py-2 border border-emerald-200 bg-white rounded-lg text-sm focus:border-emerald-500 focus:ring-emerald-500"
                  >
                    <option value="whatsapp">📱 WhatsApp</option>
                    <option value="sms">💬 SMS</option>
                    <option value="email">📧 Email</option>
                  </select>
                </div>
                <div>
                  <Label
                    htmlFor="scheduledDate"
                    className="text-emerald-700 font-medium"
                  >
                    Schedule Delivery (Optional)
                  </Label>
                  <Input
                    id="scheduledDate"
                    name="scheduledDate"
                    type="datetime-local"
                    value={formData.scheduledDate}
                    onChange={handleInputChange}
                    className="mt-2 border-emerald-200 focus:border-emerald-500 focus:ring-emerald-500"
                  />
                  <p className="text-xs text-emerald-600 mt-1">
                    Leave empty to send immediately
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Preview Section */}
          <div className="space-y-6">
            {/* Card Preview */}
            <Card className="border-emerald-100 shadow-xl">
              <CardHeader className="bg-gradient-to-r from-emerald-50 to-amber-50">
                <CardTitle className="flex items-center gap-2">
                  <Gift className="w-5 h-5 text-emerald-600" />
                  <div>
                    <span className="text-emerald-700">Preview Your Card</span>
                    <p className="text-sm font-normal text-emerald-600">
                      معاينة البطاقة
                    </p>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="relative">
                  {/* Main Card Container */}
                  <div className="relative w-full h-[500px] rounded-2xl overflow-hidden shadow-2xl transform hover:scale-105 transition-all duration-500 animate-glow">
                    {/* Background Gradient */}
                    <div
                      className={`absolute inset-0 bg-gradient-to-br ${
                        templates.find(
                          (t) => t.id === formData.selectedTemplate
                        )?.color ||
                        "from-amber-500 via-yellow-500 to-orange-600"
                      }`}
                    ></div>

                    {/* Ornate Islamic Border Pattern */}
                    <div className="absolute inset-0">
                      <div className="absolute inset-4 border-2 border-white/30 rounded-xl"></div>
                      <div className="absolute inset-6 border border-white/20 rounded-lg"></div>

                      {/* Corner Decorations */}
                      <div className="absolute top-4 left-4 w-8 h-8 border-l-2 border-t-2 border-white/40"></div>
                      <div className="absolute top-4 right-4 w-8 h-8 border-r-2 border-t-2 border-white/40"></div>
                      <div className="absolute bottom-4 left-4 w-8 h-8 border-l-2 border-b-2 border-white/40"></div>
                      <div className="absolute bottom-4 right-4 w-8 h-8 border-r-2 border-b-2 border-white/40"></div>
                    </div>

                    {/* Geometric Pattern Overlay */}
                    <div className="absolute inset-0 opacity-10">
                      <div
                        className="w-full h-full"
                        style={{
                          backgroundImage: `url("data:image/svg+xml,%3Csvg width='80' height='80' viewBox='0 0 80 80' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='white' fill-opacity='0.4'%3E%3Cpath d='M40 40l20-20v40l-20-20zm0 0l-20 20h40l-20-20z'/%3E%3Cpath d='M20 20l10-10v20l-10-10zm40 0l10-10v20l-10-10zm-40 40l10-10v20l-10-10zm40 0l10-10v20l-10-10z'/%3E%3C/g%3E%3C/svg%3E")`,
                          backgroundSize: "80px 80px",
                        }}
                      ></div>
                    </div>

                    {/* Card Content */}
                    <div className="relative z-10 h-full flex flex-col text-white p-8">
                      {/* Header Section */}
                      <div className="text-center mb-6">
                        {/* Icon */}
                        <div className="mb-4">
                          <div className="inline-flex items-center justify-center w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full border border-white/30 shadow-lg">
                            {templates.find(
                              (t) => t.id === formData.selectedTemplate
                            )?.icon || <Gift className="w-8 h-8" />}
                          </div>
                        </div>

                        {/* Main Title */}
                        <div className="space-y-2">
                          <h3 className="text-3xl font-bold tracking-wide relative">
                            <span className="relative z-10">
                              Eid Al-Adha Mubarak
                            </span>
                            <div className="absolute inset-0 animate-shimmer"></div>
                          </h3>
                          <p className="text-xl font-arabic opacity-95">
                            عيد أضحى مبارك
                          </p>
                          <div className="w-24 h-0.5 bg-white/50 mx-auto mt-3 animate-pulse"></div>
                        </div>
                      </div>

                      {/* Recipient Section */}
                      <div className="text-center mb-6">
                        <p className="text-sm opacity-80 mb-1">To / إلى</p>
                        <p className="text-2xl font-semibold">
                          {formData.recipientName || "Recipient Name"}
                        </p>
                      </div>

                      {/* Message Section */}
                      <div className="flex-1 flex items-center justify-center">
                        <div className="bg-white/15 backdrop-blur-md rounded-2xl p-6 border border-white/20 shadow-lg max-w-sm">
                          <p className="text-center text-sm leading-relaxed font-medium">
                            {formData.personalMessage ||
                              "May Allah accept your sacrifices and shower you with His countless blessings. Wishing you and your family a joyous Eid Al-Adha filled with peace, happiness, and prosperity."}
                          </p>
                        </div>
                      </div>

                      {/* Eideya Amount */}
                      {formData.eideyaAmount && (
                        <div className="text-center mb-6">
                          <div className="inline-block bg-white/25 backdrop-blur-md rounded-2xl px-8 py-4 border border-white/30 shadow-lg">
                            <div className="flex items-center gap-3">
                              <span className="text-2xl">🎁</span>
                              <div>
                                <p className="text-sm opacity-80">
                                  Eideya Gift
                                </p>
                                <p className="text-2xl font-bold">
                                  {formData.eideyaAmount} EGP
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Footer Section */}
                      <div className="text-center">
                        <p className="text-sm opacity-80 mb-1">
                          With love from / بحب من
                        </p>
                        <p className="text-lg font-semibold">
                          {formData.senderName || "Your Name"}
                        </p>
                        <div className="mt-4 flex items-center justify-center gap-2 text-xs opacity-70">
                          <span>❤️</span>
                          <span>Sent via Eideya</span>
                          <span>❤️</span>
                        </div>
                      </div>
                    </div>

                    {/* Floating Decorative Elements */}
                    <div className="absolute top-8 right-8 w-3 h-3 bg-white/40 rounded-full animate-pulse"></div>
                    <div className="absolute top-16 right-12 w-2 h-2 bg-white/30 rounded-full animate-pulse delay-300"></div>
                    <div className="absolute bottom-8 left-8 w-3 h-3 bg-white/40 rounded-full animate-pulse delay-700"></div>
                    <div className="absolute bottom-16 left-12 w-2 h-2 bg-white/30 rounded-full animate-pulse delay-1000"></div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Additional Features */}
            <Card className="border-emerald-100 shadow-lg">
              <CardHeader className="bg-gradient-to-r from-emerald-50 to-amber-50">
                <CardTitle className="flex items-center gap-2">
                  <Sparkles className="w-5 h-5 text-purple-500" />
                  <div>
                    <span className="text-emerald-700">Add More</span>
                    <p className="text-sm font-normal text-emerald-600">
                      إضافات خاصة
                    </p>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6 space-y-3">
                <Button
                  variant="outline"
                  className="w-full justify-start border-emerald-200 hover:bg-emerald-50 hover:border-emerald-300 text-emerald-700"
                >
                  <Upload className="w-4 h-4 mr-2" />
                  Upload Photo
                  <span className="ml-auto text-xs bg-amber-100 text-amber-700 px-2 py-1 rounded-full">
                    Premium
                  </span>
                </Button>
                <Button
                  variant="outline"
                  className="w-full justify-start border-emerald-200 hover:bg-emerald-50 hover:border-emerald-300 text-emerald-700"
                >
                  <Calendar className="w-4 h-4 mr-2" />
                  Record Voice Note
                  <span className="ml-auto text-xs bg-amber-100 text-amber-700 px-2 py-1 rounded-full">
                    Premium
                  </span>
                </Button>
              </CardContent>
            </Card>

            {/* Send Button */}
            <div className="relative">
              <Button
                onClick={handleSend}
                disabled={isSubmitting || completionProgress < 100}
                size="lg"
                className={`w-full py-8 text-lg font-bold shadow-xl hover:shadow-2xl transition-all duration-300 ${
                  completionProgress === 100
                    ? "bg-gradient-to-r from-emerald-600 via-green-600 to-teal-600 hover:from-emerald-700 hover:via-green-700 hover:to-teal-700 transform hover:scale-105"
                    : "bg-gray-400 cursor-not-allowed"
                }`}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="w-6 h-6 mr-3 animate-spin" />
                    <div className="text-center">
                      <div>Processing...</div>
                      <div className="text-sm opacity-90">جاري المعالجة...</div>
                    </div>
                  </>
                ) : completionProgress === 100 ? (
                  <>
                    <Send className="w-6 h-6 mr-3" />
                    <div className="text-center">
                      <div>Send Eid Card & Payment</div>
                      <div className="text-sm opacity-90">
                        إرسال بطاقة العيد والدفع
                      </div>
                    </div>
                  </>
                ) : (
                  <>
                    <AlertCircle className="w-6 h-6 mr-3" />
                    <div className="text-center">
                      <div>Complete Required Fields</div>
                      <div className="text-sm opacity-90">
                        أكمل الحقول المطلوبة
                      </div>
                    </div>
                  </>
                )}
              </Button>

              {/* Decorative elements - only show when ready */}
              {completionProgress === 100 && !isSubmitting && (
                <>
                  <div className="absolute -top-2 -right-2 w-4 h-4 bg-amber-400 rounded-full animate-pulse"></div>
                  <div className="absolute -bottom-2 -left-2 w-3 h-3 bg-rose-400 rounded-full animate-pulse delay-500"></div>
                </>
              )}

              {/* Validation status */}
              <div className="mt-4 text-center">
                {completionProgress === 100 ? (
                  <p className="text-emerald-600 text-sm flex items-center justify-center gap-1">
                    <CheckCircle className="w-4 h-4" />
                    Ready to send! All required fields completed.
                  </p>
                ) : (
                  <p className="text-amber-600 text-sm flex items-center justify-center gap-1">
                    <AlertCircle className="w-4 h-4" />
                    Please complete all required fields to proceed.
                  </p>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreateCard;
