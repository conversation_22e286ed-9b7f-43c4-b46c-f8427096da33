import { useState, useEffect } from "react";
import { useLocation, useNavigate, Link } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useToast } from "@/hooks/use-toast";
import {
  ArrowLeft,
  CreditCard,
  Smartphone,
  Building2,
  CheckCircle,
  Clock,
  Gift,
  Send,
  Loader2,
  Star,
  AlertCircle,
  Shield,
  Zap,
} from "lucide-react";

interface CardData {
  recipientName: string;
  recipientPhone: string;
  senderName: string;
  personalMessage: string;
  eideyaAmount: string;
  deliveryMethod: string;
  scheduledDate: string;
  selectedTemplate: string;
}

const SendCard = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { toast } = useToast();

  const [cardData, setCardData] = useState<CardData | null>(null);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);
  const [paymentStep, setPaymentStep] = useState(1); // 1: Payment Method, 2: Processing, 3: Success
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [transactionId, setTransactionId] = useState<string>("");

  useEffect(() => {
    if (location.state?.cardData) {
      setCardData(location.state.cardData);
    } else {
      // Redirect back if no card data
      navigate("/create-card");
    }
  }, [location.state, navigate]);

  const paymentMethods = [
    {
      id: "vodafone-cash",
      name: "Vodafone Cash",
      nameAr: "فودافون كاش",
      icon: <Smartphone className="w-6 h-6" />,
      fee: 5,
      color: "from-red-500 to-red-600",
    },
    {
      id: "instapay",
      name: "InstaPay",
      nameAr: "إنستاباي",
      icon: <CreditCard className="w-6 h-6" />,
      fee: 3,
      color: "from-blue-500 to-blue-600",
    },
    {
      id: "bank-transfer",
      name: "Bank Transfer",
      nameAr: "تحويل بنكي",
      icon: <Building2 className="w-6 h-6" />,
      fee: 10,
      color: "from-emerald-500 to-emerald-600",
    },
  ];

  const templates = [
    {
      id: "template1",
      name: "Golden Mosque",
      color: "from-amber-500 via-yellow-500 to-orange-600",
    },
    {
      id: "template2",
      name: "Emerald Crescent",
      color: "from-emerald-600 via-green-500 to-teal-600",
    },
    {
      id: "template3",
      name: "Royal Purple",
      color: "from-purple-600 via-violet-500 to-indigo-600",
    },
    {
      id: "template4",
      name: "Burgundy Elegance",
      color: "from-red-800 via-rose-600 to-pink-600",
    },
  ];

  const generateTransactionId = (): string => {
    const timestamp = Date.now().toString();
    const random = Math.random().toString(36).substring(2, 8).toUpperCase();
    return `EID${timestamp.slice(-6)}${random}`;
  };

  const handlePayment = async () => {
    if (!selectedPaymentMethod) {
      toast({
        title: "Payment Method Required",
        description: "Please select a payment method to proceed.",
        variant: "destructive",
      });
      return;
    }

    try {
      setError(null);
      setIsProcessing(true);
      setPaymentStep(2);
      setProgress(0);

      // Generate transaction ID
      const txId = generateTransactionId();
      setTransactionId(txId);

      toast({
        title: "Payment Processing Started",
        description: `Transaction ID: ${txId}`,
      });

      // Simulate realistic payment processing with potential failure
      const progressInterval = setInterval(() => {
        setProgress((prev) => {
          if (prev >= 100) {
            clearInterval(progressInterval);

            // Simulate 5% chance of payment failure for demo
            const shouldFail = Math.random() < 0.05;

            if (shouldFail) {
              setError(
                "Payment failed. Please try again or use a different payment method."
              );
              setPaymentStep(1);
              setIsProcessing(false);
              toast({
                title: "Payment Failed",
                description:
                  "There was an issue processing your payment. Please try again.",
                variant: "destructive",
              });
            } else {
              setPaymentStep(3);
              setIsProcessing(false);
              toast({
                title: "Payment Successful! 🎉",
                description: `Your Eid card has been sent to ${cardData?.recipientName}!`,
              });
            }
            return 100;
          }
          return prev + 10;
        });
      }, 300);
    } catch (error) {
      setError("An unexpected error occurred. Please try again.");
      setPaymentStep(1);
      setIsProcessing(false);
      toast({
        title: "Error",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      });
    }
  };

  const calculateTotal = () => {
    const eideyaAmount = parseFloat(cardData?.eideyaAmount || "0");
    const selectedMethod = paymentMethods.find(
      (m) => m.id === selectedPaymentMethod
    );
    const fee = selectedMethod?.fee || 0;
    return eideyaAmount + fee;
  };

  if (!cardData) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4" />
          <p>Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-amber-50 to-rose-50 relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0 opacity-5">
        <div
          className="absolute top-0 left-0 w-full h-full"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23059669' fill-opacity='0.4'%3E%3Cpath d='M30 30l15-15v30l-15-15zm0 0l-15 15h30l-15-15z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            backgroundSize: "60px 60px",
          }}
        ></div>
      </div>

      {/* Header */}
      <div className="bg-white/90 backdrop-blur-md border-b border-emerald-100 shadow-lg relative z-10">
        <div className="container mx-auto px-6 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Link
                to="/create-card"
                className="flex items-center gap-2 text-emerald-600 hover:text-emerald-700 transition-colors"
              >
                <ArrowLeft className="w-5 h-5" />
                <span className="font-medium">Back to Create</span>
              </Link>
              <div className="h-6 w-px bg-emerald-200"></div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-emerald-700 to-amber-600 bg-clip-text text-transparent">
                  {paymentStep === 1 && "Complete Your Payment"}
                  {paymentStep === 2 && "Processing Payment"}
                  {paymentStep === 3 && "Payment Successful!"}
                </h1>
                <p className="text-sm text-emerald-600">
                  {paymentStep === 1 && "أكمل عملية الدفع"}
                  {paymentStep === 2 && "جاري معالجة الدفع"}
                  {paymentStep === 3 && "تم الدفع بنجاح!"}
                </p>
              </div>
            </div>
            <div className="hidden md:flex items-center gap-2 text-emerald-700">
              <Gift className="w-5 h-5" />
              <span className="text-sm font-medium">Eid Al-Adha Mubarak</span>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-6 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Error Alert */}
          {error && (
            <div className="mb-6">
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            </div>
          )}
          {/* Progress Steps */}
          <div className="mb-8">
            <div className="flex items-center justify-center space-x-8">
              <div className="flex items-center">
                <div
                  className={`w-8 h-8 rounded-full flex items-center justify-center ${
                    paymentStep >= 1
                      ? "bg-emerald-500 text-white"
                      : "bg-gray-200 text-gray-500"
                  }`}
                >
                  1
                </div>
                <span className="ml-2 text-sm font-medium">Payment</span>
              </div>
              <div
                className={`h-1 w-16 ${
                  paymentStep >= 2 ? "bg-emerald-500" : "bg-gray-200"
                }`}
              ></div>
              <div className="flex items-center">
                <div
                  className={`w-8 h-8 rounded-full flex items-center justify-center ${
                    paymentStep >= 2
                      ? "bg-emerald-500 text-white"
                      : "bg-gray-200 text-gray-500"
                  }`}
                >
                  2
                </div>
                <span className="ml-2 text-sm font-medium">Processing</span>
              </div>
              <div
                className={`h-1 w-16 ${
                  paymentStep >= 3 ? "bg-emerald-500" : "bg-gray-200"
                }`}
              ></div>
              <div className="flex items-center">
                <div
                  className={`w-8 h-8 rounded-full flex items-center justify-center ${
                    paymentStep >= 3
                      ? "bg-emerald-500 text-white"
                      : "bg-gray-200 text-gray-500"
                  }`}
                >
                  {paymentStep >= 3 ? <CheckCircle className="w-4 h-4" /> : "3"}
                </div>
                <span className="ml-2 text-sm font-medium">Complete</span>
              </div>
            </div>
          </div>

          <div className="grid lg:grid-cols-2 gap-8">
            {/* Order Summary */}
            <Card className="border-emerald-100 shadow-lg h-fit">
              <CardHeader className="bg-gradient-to-r from-emerald-50 to-amber-50">
                <CardTitle className="flex items-center gap-2">
                  <Gift className="w-5 h-5 text-emerald-600" />
                  <div>
                    <span className="text-emerald-700">Order Summary</span>
                    <p className="text-sm font-normal text-emerald-600">
                      ملخص الطلب
                    </p>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6 space-y-4">
                {/* Card Preview */}
                <div className="relative">
                  <div className="relative w-full h-64 rounded-xl overflow-hidden shadow-xl">
                    {/* Background Gradient */}
                    <div
                      className={`absolute inset-0 bg-gradient-to-br ${
                        templates.find(
                          (t) => t.id === cardData.selectedTemplate
                        )?.color ||
                        "from-amber-500 via-yellow-500 to-orange-600"
                      }`}
                    ></div>

                    {/* Ornate Border */}
                    <div className="absolute inset-0">
                      <div className="absolute inset-2 border border-white/30 rounded-lg"></div>
                      <div className="absolute top-2 left-2 w-4 h-4 border-l border-t border-white/40"></div>
                      <div className="absolute top-2 right-2 w-4 h-4 border-r border-t border-white/40"></div>
                      <div className="absolute bottom-2 left-2 w-4 h-4 border-l border-b border-white/40"></div>
                      <div className="absolute bottom-2 right-2 w-4 h-4 border-r border-b border-white/40"></div>
                    </div>

                    {/* Geometric Pattern */}
                    <div className="absolute inset-0 opacity-10">
                      <div
                        className="w-full h-full"
                        style={{
                          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='white' fill-opacity='0.4'%3E%3Cpath d='M30 30l15-15v30l-15-15zm0 0l-15 15h30l-15-15z'/%3E%3C/g%3E%3C/svg%3E")`,
                          backgroundSize: "60px 60px",
                        }}
                      ></div>
                    </div>

                    {/* Card Content */}
                    <div className="relative z-10 h-full flex flex-col text-white p-4">
                      {/* Header */}
                      <div className="text-center mb-3">
                        <div className="inline-flex items-center justify-center w-8 h-8 bg-white/20 rounded-full mb-2">
                          <Gift className="w-4 h-4" />
                        </div>
                        <h3 className="text-lg font-bold">
                          Eid Al-Adha Mubarak
                        </h3>
                        <p className="text-xs opacity-90 font-arabic">
                          عيد أضحى مبارك
                        </p>
                      </div>

                      {/* Recipient */}
                      <div className="text-center mb-3">
                        <p className="text-xs opacity-80">To / إلى</p>
                        <p className="text-sm font-semibold">
                          {cardData.recipientName}
                        </p>
                      </div>

                      {/* Message */}
                      <div className="flex-1 flex items-center justify-center">
                        <div className="bg-white/15 backdrop-blur-sm rounded-lg p-3 text-center">
                          <p className="text-xs leading-relaxed">
                            {cardData.personalMessage.length > 60
                              ? cardData.personalMessage.substring(0, 60) +
                                "..."
                              : cardData.personalMessage || "Eid blessings..."}
                          </p>
                        </div>
                      </div>

                      {/* Eideya Amount */}
                      {cardData.eideyaAmount && (
                        <div className="text-center mb-3">
                          <div className="inline-block bg-white/25 backdrop-blur-sm rounded-lg px-3 py-2">
                            <div className="flex items-center gap-2">
                              <span className="text-sm">🎁</span>
                              <div>
                                <p className="text-xs opacity-80">Eideya</p>
                                <p className="text-sm font-bold">
                                  {cardData.eideyaAmount} EGP
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Footer */}
                      <div className="text-center">
                        <p className="text-xs opacity-80">From / من</p>
                        <p className="text-sm font-semibold">
                          {cardData.senderName}
                        </p>
                      </div>
                    </div>

                    {/* Decorative dots */}
                    <div className="absolute top-3 right-3 w-1.5 h-1.5 bg-white/40 rounded-full animate-pulse"></div>
                    <div className="absolute bottom-3 left-3 w-1.5 h-1.5 bg-white/40 rounded-full animate-pulse delay-500"></div>
                  </div>
                </div>

                <Separator />

                {/* Order Details */}
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Recipient:</span>
                    <span className="font-medium">
                      {cardData.recipientName}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Phone:</span>
                    <span className="font-medium">
                      {cardData.recipientPhone}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Delivery:</span>
                    <Badge variant="outline" className="capitalize">
                      {cardData.deliveryMethod}
                    </Badge>
                  </div>
                  {cardData.scheduledDate && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Scheduled:</span>
                      <span className="font-medium text-sm">
                        {new Date(cardData.scheduledDate).toLocaleString()}
                      </span>
                    </div>
                  )}
                </div>

                <Separator />

                {/* Pricing */}
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Eideya Amount:</span>
                    <span className="font-medium">
                      {cardData.eideyaAmount} EGP
                    </span>
                  </div>
                  {selectedPaymentMethod && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Service Fee:</span>
                      <span className="font-medium">
                        {
                          paymentMethods.find(
                            (m) => m.id === selectedPaymentMethod
                          )?.fee
                        }{" "}
                        EGP
                      </span>
                    </div>
                  )}
                  <Separator />
                  <div className="flex justify-between text-lg font-bold">
                    <span>Total:</span>
                    <span className="text-emerald-600">
                      {calculateTotal()} EGP
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Payment Section */}
            <div className="space-y-6">
              {paymentStep === 1 && (
                <Card className="border-emerald-100 shadow-lg">
                  <CardHeader className="bg-gradient-to-r from-emerald-50 to-amber-50">
                    <CardTitle className="flex items-center gap-2">
                      <CreditCard className="w-5 h-5 text-emerald-600" />
                      <div>
                        <span className="text-emerald-700">
                          Choose Payment Method
                        </span>
                        <p className="text-sm font-normal text-emerald-600">
                          اختر طريقة الدفع
                        </p>
                      </div>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-6">
                    <div className="space-y-4">
                      {paymentMethods.map((method) => (
                        <button
                          key={method.id}
                          onClick={() => setSelectedPaymentMethod(method.id)}
                          className={`w-full p-4 rounded-xl border-2 transition-all duration-300 hover:scale-105 ${
                            selectedPaymentMethod === method.id
                              ? "border-emerald-500 bg-emerald-50 shadow-lg"
                              : "border-gray-200 hover:border-emerald-300 hover:shadow-md"
                          }`}
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <div
                                className={`w-12 h-12 rounded-lg bg-gradient-to-r ${method.color} flex items-center justify-center text-white`}
                              >
                                {method.icon}
                              </div>
                              <div className="text-left">
                                <p className="font-semibold text-gray-800">
                                  {method.name}
                                </p>
                                <p className="text-sm text-emerald-600">
                                  {method.nameAr}
                                </p>
                              </div>
                            </div>
                            <div className="text-right">
                              <p className="text-sm text-gray-600">
                                Fee: {method.fee} EGP
                              </p>
                              {selectedPaymentMethod === method.id && (
                                <Star className="w-4 h-4 text-emerald-500 fill-current ml-auto mt-1" />
                              )}
                            </div>
                          </div>
                        </button>
                      ))}
                    </div>

                    <Button
                      onClick={handlePayment}
                      disabled={!selectedPaymentMethod}
                      size="lg"
                      className="w-full mt-6 bg-gradient-to-r from-emerald-600 to-green-600 hover:from-emerald-700 hover:to-green-700 py-6 text-lg"
                    >
                      <Send className="w-5 h-5 mr-2" />
                      Complete Payment & Send Card
                    </Button>
                  </CardContent>
                </Card>
              )}

              {paymentStep === 2 && (
                <Card className="border-emerald-100 shadow-lg">
                  <CardContent className="p-8 text-center">
                    <div className="mb-6">
                      <Loader2 className="w-16 h-16 animate-spin text-emerald-600 mx-auto mb-4" />
                      <h3 className="text-xl font-bold text-gray-800 mb-2">
                        Processing Your Payment
                      </h3>
                      <p className="text-emerald-600">
                        جاري معالجة عملية الدفع
                      </p>
                    </div>

                    <div className="space-y-4">
                      <Progress value={progress} className="w-full" />
                      <p className="text-sm text-gray-600">
                        Please wait while we process your payment and prepare
                        your Eid card...
                      </p>
                    </div>
                  </CardContent>
                </Card>
              )}

              {paymentStep === 3 && (
                <Card className="border-emerald-100 shadow-lg">
                  <CardContent className="p-8 text-center">
                    <div className="mb-6">
                      <div className="w-16 h-16 bg-emerald-100 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse">
                        <CheckCircle className="w-10 h-10 text-emerald-600" />
                      </div>
                      <h3 className="text-2xl font-bold text-gray-800 mb-2">
                        Payment Successful! 🎉
                      </h3>
                      <p className="text-emerald-600 mb-4">تم الدفع بنجاح!</p>
                      <p className="text-gray-600">
                        Your Eid card has been sent to {cardData.recipientName}{" "}
                        via {cardData.deliveryMethod}!
                      </p>
                    </div>

                    <div className="space-y-4">
                      {/* Transaction Details */}
                      <div className="bg-emerald-50 rounded-lg p-4 space-y-3">
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-gray-600">Transaction ID:</span>
                          <span className="font-mono font-semibold text-emerald-700">
                            {transactionId}
                          </span>
                        </div>
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-gray-600">Payment Method:</span>
                          <span className="font-medium">
                            {
                              paymentMethods.find(
                                (m) => m.id === selectedPaymentMethod
                              )?.name
                            }
                          </span>
                        </div>
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-gray-600">Total Amount:</span>
                          <span className="font-bold text-emerald-700">
                            {calculateTotal()} EGP
                          </span>
                        </div>
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-gray-600">Date & Time:</span>
                          <span className="font-medium">
                            {new Date().toLocaleString()}
                          </span>
                        </div>
                      </div>

                      {/* Delivery Status */}
                      <div className="bg-blue-50 rounded-lg p-4">
                        <div className="flex items-center justify-center gap-2 text-blue-700 mb-2">
                          <Clock className="w-4 h-4" />
                          <span className="font-semibold">Delivery Status</span>
                        </div>
                        <p className="text-sm text-blue-600">
                          {cardData.scheduledDate
                            ? `Scheduled for delivery on ${new Date(
                                cardData.scheduledDate
                              ).toLocaleString()}`
                            : "Delivered immediately to recipient's device"}
                        </p>
                      </div>

                      {/* Security Notice */}
                      <div className="bg-amber-50 rounded-lg p-4">
                        <div className="flex items-center justify-center gap-2 text-amber-700 mb-2">
                          <Shield className="w-4 h-4" />
                          <span className="font-semibold">Security Notice</span>
                        </div>
                        <p className="text-xs text-amber-600">
                          Keep your transaction ID safe. You can use it to track
                          your payment or for customer support.
                        </p>
                      </div>

                      <div className="flex gap-3">
                        <Button
                          onClick={() => navigate("/create-card")}
                          variant="outline"
                          className="flex-1"
                        >
                          Send Another Card
                        </Button>
                        <Button
                          onClick={() => navigate("/")}
                          className="flex-1 bg-gradient-to-r from-emerald-600 to-green-600"
                        >
                          Back to Home
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SendCard;
